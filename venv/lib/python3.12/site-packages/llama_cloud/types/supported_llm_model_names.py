# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class SupportedLlmModelNames(str, enum.Enum):
    GPT_4_O = "GPT_4O"
    GPT_4_O_MINI = "GPT_4O_MINI"
    GPT_4_1 = "GPT_4_1"
    GPT_4_1_NANO = "GPT_4_1_NANO"
    GPT_4_1_MINI = "GPT_4_1_MINI"
    AZURE_OPENAI_GPT_4_O = "AZURE_OPENAI_GPT_4O"
    AZURE_OPENAI_GPT_4_O_MINI = "AZURE_OPENAI_GPT_4O_MINI"
    CLAUDE_3_5_SONNET = "CLAUDE_3_5_SONNET"
    BEDROCK_CLAUDE_3_5_SONNET_V_1 = "BEDROCK_CLAUDE_3_5_SONNET_V1"
    BEDROCK_CLAUDE_3_5_SONNET_V_2 = "BEDROCK_CLAUDE_3_5_SONNET_V2"
    VERTEX_AI_CLAUDE_3_5_SONNET_V_2 = "VERTEX_AI_CLAUDE_3_5_SONNET_V2"

    def visit(
        self,
        gpt_4_o: typing.Callable[[], T_Result],
        gpt_4_o_mini: typing.Callable[[], T_Result],
        gpt_4_1: typing.Callable[[], T_Result],
        gpt_4_1_nano: typing.Callable[[], T_Result],
        gpt_4_1_mini: typing.Callable[[], T_Result],
        azure_openai_gpt_4_o: typing.Callable[[], T_Result],
        azure_openai_gpt_4_o_mini: typing.Callable[[], T_Result],
        claude_3_5_sonnet: typing.Callable[[], T_Result],
        bedrock_claude_3_5_sonnet_v_1: typing.Callable[[], T_Result],
        bedrock_claude_3_5_sonnet_v_2: typing.Callable[[], T_Result],
        vertex_ai_claude_3_5_sonnet_v_2: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is SupportedLlmModelNames.GPT_4_O:
            return gpt_4_o()
        if self is SupportedLlmModelNames.GPT_4_O_MINI:
            return gpt_4_o_mini()
        if self is SupportedLlmModelNames.GPT_4_1:
            return gpt_4_1()
        if self is SupportedLlmModelNames.GPT_4_1_NANO:
            return gpt_4_1_nano()
        if self is SupportedLlmModelNames.GPT_4_1_MINI:
            return gpt_4_1_mini()
        if self is SupportedLlmModelNames.AZURE_OPENAI_GPT_4_O:
            return azure_openai_gpt_4_o()
        if self is SupportedLlmModelNames.AZURE_OPENAI_GPT_4_O_MINI:
            return azure_openai_gpt_4_o_mini()
        if self is SupportedLlmModelNames.CLAUDE_3_5_SONNET:
            return claude_3_5_sonnet()
        if self is SupportedLlmModelNames.BEDROCK_CLAUDE_3_5_SONNET_V_1:
            return bedrock_claude_3_5_sonnet_v_1()
        if self is SupportedLlmModelNames.BEDROCK_CLAUDE_3_5_SONNET_V_2:
            return bedrock_claude_3_5_sonnet_v_2()
        if self is SupportedLlmModelNames.VERTEX_AI_CLAUDE_3_5_SONNET_V_2:
            return vertex_ai_claude_3_5_sonnet_v_2()
