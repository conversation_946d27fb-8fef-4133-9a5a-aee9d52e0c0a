# This file was auto-generated by <PERSON>rn from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class StatusEnum(str, enum.Enum):
    """
    Enum for representing the status of a job
    """

    PENDING = "PENDING"
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    PARTIAL_SUCCESS = "PARTIAL_SUCCESS"
    CANCELLED = "CANCELLED"

    def visit(
        self,
        pending: typing.Callable[[], T_Result],
        success: typing.Callable[[], T_Result],
        error: typing.Callable[[], T_Result],
        partial_success: typing.Callable[[], T_Result],
        cancelled: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is StatusEnum.PENDING:
            return pending()
        if self is StatusEnum.SUCCESS:
            return success()
        if self is StatusEnum.ERROR:
            return error()
        if self is StatusEnum.PARTIAL_SUCCESS:
            return partial_success()
        if self is StatusEnum.CANCELLED:
            return cancelled()
