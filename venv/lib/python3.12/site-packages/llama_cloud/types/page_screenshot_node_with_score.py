# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .page_screenshot_metadata import PageScreenshotMetadata

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class PageScreenshotNodeWithScore(pydantic.BaseModel):
    """
    Page screenshot metadata with score
    """

    node: PageScreenshotMetadata
    score: float = pydantic.Field(description="The score of the screenshot node")
    class_name: typing.Optional[str]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
