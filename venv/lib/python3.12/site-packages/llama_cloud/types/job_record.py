# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .job_names import JobNames
from .job_record_parameters import JobRecordParameters
from .status_enum import StatusEnum

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class JobRecord(pydantic.BaseModel):
    """
    Schema for a job's metadata.
    """

    job_name: JobNames = pydantic.Field(description="The name of the job.")
    partitions: typing.Dict[str, str] = pydantic.Field(
        description="The partitions for this execution. Used for determining where to save job output."
    )
    parameters: typing.Optional[JobRecordParameters]
    session_id: typing.Optional[str]
    correlation_id: typing.Optional[str]
    parent_job_execution_id: typing.Optional[str]
    user_id: typing.Optional[str]
    created_at: dt.datetime = pydantic.Field(description="Creation datetime")
    project_id: typing.Optional[str]
    id: typing.Optional[str] = pydantic.Field(description="Unique identifier")
    status: StatusEnum
    error_code: typing.Optional[str]
    error_message: typing.Optional[str]
    attempts: typing.Optional[int]
    started_at: typing.Optional[dt.datetime]
    ended_at: typing.Optional[dt.datetime]
    updated_at: typing.Optional[dt.datetime] = pydantic.Field(description="Update datetime")

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
