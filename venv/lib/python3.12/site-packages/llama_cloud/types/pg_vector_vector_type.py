# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class PgVectorVectorType(str, enum.Enum):
    """
    Vector storage formats for PGVector.
    Docs:
    https://github.com/pgvector/pgvector?tab=readme-ov-file#query-options
    """

    VECTOR = "vector"
    HALF_VEC = "half_vec"
    BIT = "bit"
    SPARSE_VEC = "sparse_vec"

    def visit(
        self,
        vector: typing.Callable[[], T_Result],
        half_vec: typing.Callable[[], T_Result],
        bit: typing.Callable[[], T_Result],
        sparse_vec: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is PgVectorVectorType.VECTOR:
            return vector()
        if self is PgVectorVectorType.HALF_VEC:
            return half_vec()
        if self is PgVectorVectorType.BIT:
            return bit()
        if self is PgVectorVectorType.SPARSE_VEC:
            return sparse_vec()
