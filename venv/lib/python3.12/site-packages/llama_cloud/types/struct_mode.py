# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class StructMode(str, enum.Enum):
    STRUCT_PARSE = "STRUCT_PARSE"
    JSON_MODE = "JSON_MODE"
    FUNC_CALL = "FUNC_CALL"
    STRUCT_RELAXED = "STRUCT_RELAXED"
    UNSTRUCTURED = "UNSTRUCTURED"

    def visit(
        self,
        struct_parse: typing.Callable[[], T_Result],
        json_mode: typing.Callable[[], T_Result],
        func_call: typing.Callable[[], T_Result],
        struct_relaxed: typing.Callable[[], T_Result],
        unstructured: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is StructMode.STRUCT_PARSE:
            return struct_parse()
        if self is StructMode.JSON_MODE:
            return json_mode()
        if self is StructMode.FUNC_CALL:
            return func_call()
        if self is StructMode.STRUCT_RELAXED:
            return struct_relaxed()
        if self is StructMode.UNSTRUCTURED:
            return unstructured()
