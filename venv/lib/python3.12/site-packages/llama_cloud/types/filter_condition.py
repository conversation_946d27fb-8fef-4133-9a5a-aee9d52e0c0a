# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class FilterCondition(str, enum.Enum):
    """
    Vector store filter conditions to combine different filters.
    """

    AND = "and"
    OR = "or"
    NOT = "not"

    def visit(
        self,
        and_: typing.Callable[[], T_Result],
        or_: typing.Callable[[], T_Result],
        not_: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is FilterCondition.AND:
            return and_()
        if self is FilterCondition.OR:
            return or_()
        if self is FilterCondition.NOT:
            return not_()
