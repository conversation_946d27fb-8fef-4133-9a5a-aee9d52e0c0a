# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class PipelineStatus(str, enum.Enum):
    CREATED = "CREATED"
    DELETING = "DELETING"

    def visit(self, created: typing.Callable[[], T_Result], deleting: typing.Callable[[], T_Result]) -> T_Result:
        if self is PipelineStatus.CREATED:
            return created()
        if self is PipelineStatus.DELETING:
            return deleting()
