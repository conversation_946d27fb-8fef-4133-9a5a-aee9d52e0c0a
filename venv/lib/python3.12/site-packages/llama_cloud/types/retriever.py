# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .retriever_pipeline import RetrieverPipeline

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class Retriever(pydantic.BaseModel):
    """
    An entity that retrieves context nodes from several sub RetrieverTools.
    """

    name: str = pydantic.Field(
        description="A name for the retriever tool. Will default to the pipeline name if not provided."
    )
    pipelines: typing.Optional[typing.List[RetrieverPipeline]] = pydantic.Field(
        description="The pipelines this retriever uses."
    )
    id: str = pydantic.Field(description="Unique identifier")
    created_at: typing.Optional[dt.datetime]
    updated_at: typing.Optional[dt.datetime]
    project_id: str = pydantic.Field(description="The ID of the project this retriever resides in.")

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
