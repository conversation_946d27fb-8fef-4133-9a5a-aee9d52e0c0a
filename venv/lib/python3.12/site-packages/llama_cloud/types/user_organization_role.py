# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .role import Role

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class UserOrganizationRole(pydantic.BaseModel):
    """
    Schema for a user's role in an organization.
    """

    id: str = pydantic.Field(description="Unique identifier")
    created_at: typing.Optional[dt.datetime]
    updated_at: typing.Optional[dt.datetime]
    user_id: str = pydantic.Field(description="The user's ID.")
    organization_id: str = pydantic.Field(description="The organization's ID.")
    project_ids: typing.Optional[typing.List[str]]
    role_id: str = pydantic.Field(description="The role's ID.")
    role: Role = pydantic.Field(description="The role.")

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
