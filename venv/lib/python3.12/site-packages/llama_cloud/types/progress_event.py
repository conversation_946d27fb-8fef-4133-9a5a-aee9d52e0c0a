# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .progress_event_status import ProgressEventStatus
from .report_event_type import ReportEventType

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class ProgressEvent(pydantic.BaseModel):
    """
    Event for tracking progress of operations in workflows.
    """

    timestamp: typing.Optional[dt.datetime]
    id: typing.Optional[str] = pydantic.Field(description="The ID of the event")
    group_id: typing.Optional[str] = pydantic.Field(description="The ID of the group this event belongs to")
    variant: ReportEventType
    msg: str = pydantic.Field(description="The message to display to the user")
    progress: typing.Optional[float]
    status: typing.Optional[ProgressEventStatus] = pydantic.Field(description="Current status of the operation")
    extra_detail: typing.Optional[typing.Dict[str, typing.Any]]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
