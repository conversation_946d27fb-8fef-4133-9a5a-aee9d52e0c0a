# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .data_sink import DataSink
from .eval_execution_params import EvalExecutionParams
from .llama_parse_parameters import LlamaParseParameters
from .pipeline_configuration_hashes import PipelineConfigurationHashes
from .pipeline_embedding_config import PipelineEmbeddingConfig
from .pipeline_metadata_config import PipelineMetadataConfig
from .pipeline_status import PipelineStatus
from .pipeline_transform_config import PipelineTransformConfig
from .pipeline_type import PipelineType
from .preset_retrieval_params import PresetRetrievalParams

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class Pipeline(pydantic.BaseModel):
    """
    Schema for a pipeline.
    """

    id: str = pydantic.Field(description="Unique identifier")
    created_at: typing.Optional[dt.datetime]
    updated_at: typing.Optional[dt.datetime]
    name: str
    project_id: str
    embedding_model_config_id: typing.Optional[str]
    pipeline_type: typing.Optional[PipelineType] = pydantic.Field(
        description="Type of pipeline. Either PLAYGROUND or MANAGED."
    )
    managed_pipeline_id: typing.Optional[str]
    embedding_config: PipelineEmbeddingConfig
    config_hash: typing.Optional[PipelineConfigurationHashes]
    transform_config: typing.Optional[PipelineTransformConfig] = pydantic.Field(
        description="Configuration for the transformation."
    )
    preset_retrieval_parameters: typing.Optional[PresetRetrievalParams] = pydantic.Field(
        description="Preset retrieval parameters for the pipeline."
    )
    eval_parameters: typing.Optional[EvalExecutionParams] = pydantic.Field(
        description="Eval parameters for the pipeline."
    )
    llama_parse_parameters: typing.Optional[LlamaParseParameters]
    data_sink: typing.Optional[DataSink]
    status: typing.Optional[PipelineStatus]
    metadata_config: typing.Optional[PipelineMetadataConfig]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
