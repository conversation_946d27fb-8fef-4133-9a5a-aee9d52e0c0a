# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class FilterOperator(str, enum.Enum):
    """
    Vector store filter operator.
    """

    EQUAL_TO = "=="
    GREATER_THAN = ">"
    LESS_THAN = "<"
    NOT_EQUALS = "!="
    GREATER_THAN_OR_EQUAL_TO = ">="
    LESS_THAN_OR_EQUAL_TO = "<="
    IN = "in"
    NIN = "nin"
    ANY = "any"
    ALL = "all"
    TEXT_MATCH = "text_match"
    TEXT_MATCH_INSENSITIVE = "text_match_insensitive"
    CONTAINS = "contains"
    IS_EMPTY = "is_empty"

    def visit(
        self,
        equal_to: typing.Callable[[], T_Result],
        greater_than: typing.Callable[[], T_Result],
        less_than: typing.Callable[[], T_Result],
        not_equals: typing.Callable[[], T_Result],
        greater_than_or_equal_to: typing.Callable[[], T_Result],
        less_than_or_equal_to: typing.Callable[[], T_Result],
        in_: typing.Callable[[], T_Result],
        nin: typing.Callable[[], T_Result],
        any: typing.Callable[[], T_Result],
        all: typing.Callable[[], T_Result],
        text_match: typing.Callable[[], T_Result],
        text_match_insensitive: typing.Callable[[], T_Result],
        contains: typing.Callable[[], T_Result],
        is_empty: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is FilterOperator.EQUAL_TO:
            return equal_to()
        if self is FilterOperator.GREATER_THAN:
            return greater_than()
        if self is FilterOperator.LESS_THAN:
            return less_than()
        if self is FilterOperator.NOT_EQUALS:
            return not_equals()
        if self is FilterOperator.GREATER_THAN_OR_EQUAL_TO:
            return greater_than_or_equal_to()
        if self is FilterOperator.LESS_THAN_OR_EQUAL_TO:
            return less_than_or_equal_to()
        if self is FilterOperator.IN:
            return in_()
        if self is FilterOperator.NIN:
            return nin()
        if self is FilterOperator.ANY:
            return any()
        if self is FilterOperator.ALL:
            return all()
        if self is FilterOperator.TEXT_MATCH:
            return text_match()
        if self is FilterOperator.TEXT_MATCH_INSENSITIVE:
            return text_match_insensitive()
        if self is FilterOperator.CONTAINS:
            return contains()
        if self is FilterOperator.IS_EMPTY:
            return is_empty()
