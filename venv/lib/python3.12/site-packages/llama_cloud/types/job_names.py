# This file was auto-generated by <PERSON>rn from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class JobNames(str, enum.Enum):
    """
    Enum for executable pipeline job names.
    """

    LOAD_DOCUMENTS_JOB = "load_documents_job"
    LOAD_FILES_JOB = "load_files_job"
    PLAYGROUND_JOB = "playground_job"
    PIPELINE_MANAGED_INGESTION_JOB = "pipeline_managed_ingestion_job"
    DATA_SOURCE_MANAGED_INGESTION_JOB = "data_source_managed_ingestion_job"
    DATA_SOURCE_UPDATE_DISPATCHER_JOB = "data_source_update_dispatcher_job"
    PIPELINE_FILE_UPDATE_DISPATCHER_JOB = "pipeline_file_update_dispatcher_job"
    PIPELINE_FILE_UPDATER_JOB = "pipeline_file_updater_job"
    FILE_MANAGED_INGESTION_JOB = "file_managed_ingestion_job"
    DOCUMENT_INGESTION_JOB = "document_ingestion_job"
    METADATA_UPDATE_JOB = "metadata_update_job"
    PARSE_RAW_FILE_JOB_CACHED = "parse_raw_file_job_cached"
    EXTRACTION_JOB = "extraction_job"
    EXTRACT_JOB = "extract_job"
    ASYNCIO_TEST_JOB = "asyncio_test_job"
    PARSE_RAW_FILE_JOB = "parse_raw_file_job"
    LLAMA_PARSE_TRANSFORM_JOB = "llama_parse_transform_job"

    def visit(
        self,
        load_documents_job: typing.Callable[[], T_Result],
        load_files_job: typing.Callable[[], T_Result],
        playground_job: typing.Callable[[], T_Result],
        pipeline_managed_ingestion_job: typing.Callable[[], T_Result],
        data_source_managed_ingestion_job: typing.Callable[[], T_Result],
        data_source_update_dispatcher_job: typing.Callable[[], T_Result],
        pipeline_file_update_dispatcher_job: typing.Callable[[], T_Result],
        pipeline_file_updater_job: typing.Callable[[], T_Result],
        file_managed_ingestion_job: typing.Callable[[], T_Result],
        document_ingestion_job: typing.Callable[[], T_Result],
        metadata_update_job: typing.Callable[[], T_Result],
        parse_raw_file_job_cached: typing.Callable[[], T_Result],
        extraction_job: typing.Callable[[], T_Result],
        extract_job: typing.Callable[[], T_Result],
        asyncio_test_job: typing.Callable[[], T_Result],
        parse_raw_file_job: typing.Callable[[], T_Result],
        llama_parse_transform_job: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is JobNames.LOAD_DOCUMENTS_JOB:
            return load_documents_job()
        if self is JobNames.LOAD_FILES_JOB:
            return load_files_job()
        if self is JobNames.PLAYGROUND_JOB:
            return playground_job()
        if self is JobNames.PIPELINE_MANAGED_INGESTION_JOB:
            return pipeline_managed_ingestion_job()
        if self is JobNames.DATA_SOURCE_MANAGED_INGESTION_JOB:
            return data_source_managed_ingestion_job()
        if self is JobNames.DATA_SOURCE_UPDATE_DISPATCHER_JOB:
            return data_source_update_dispatcher_job()
        if self is JobNames.PIPELINE_FILE_UPDATE_DISPATCHER_JOB:
            return pipeline_file_update_dispatcher_job()
        if self is JobNames.PIPELINE_FILE_UPDATER_JOB:
            return pipeline_file_updater_job()
        if self is JobNames.FILE_MANAGED_INGESTION_JOB:
            return file_managed_ingestion_job()
        if self is JobNames.DOCUMENT_INGESTION_JOB:
            return document_ingestion_job()
        if self is JobNames.METADATA_UPDATE_JOB:
            return metadata_update_job()
        if self is JobNames.PARSE_RAW_FILE_JOB_CACHED:
            return parse_raw_file_job_cached()
        if self is JobNames.EXTRACTION_JOB:
            return extraction_job()
        if self is JobNames.EXTRACT_JOB:
            return extract_job()
        if self is JobNames.ASYNCIO_TEST_JOB:
            return asyncio_test_job()
        if self is JobNames.PARSE_RAW_FILE_JOB:
            return parse_raw_file_job()
        if self is JobNames.LLAMA_PARSE_TRANSFORM_JOB:
            return llama_parse_transform_job()
