# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .metadata_filters import MetadataFilters
from .preset_retrieval_params_search_filters_inference_schema_value import (
    PresetRetrievalParamsSearchFiltersInferenceSchemaValue,
)
from .retrieval_mode import RetrievalMode

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class PresetRetrievalParams(pydantic.BaseModel):
    """
    Schema for the search params for an retrieval execution that can be preset for a pipeline.
    """

    dense_similarity_top_k: typing.Optional[int]
    dense_similarity_cutoff: typing.Optional[float]
    sparse_similarity_top_k: typing.Optional[int]
    enable_reranking: typing.Optional[bool]
    rerank_top_n: typing.Optional[int]
    alpha: typing.Optional[float]
    search_filters: typing.Optional[MetadataFilters]
    search_filters_inference_schema: typing.Optional[
        typing.Dict[str, typing.Optional[PresetRetrievalParamsSearchFiltersInferenceSchemaValue]]
    ]
    files_top_k: typing.Optional[int]
    retrieval_mode: typing.Optional[RetrievalMode] = pydantic.Field(description="The retrieval mode for the query.")
    retrieve_image_nodes: typing.Optional[bool] = pydantic.Field(description="Whether to retrieve image nodes.")
    retrieve_page_screenshot_nodes: typing.Optional[bool] = pydantic.Field(
        description="Whether to retrieve page screenshot nodes."
    )
    retrieve_page_figure_nodes: typing.Optional[bool] = pydantic.Field(
        description="Whether to retrieve page figure nodes."
    )
    class_name: typing.Optional[str]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
