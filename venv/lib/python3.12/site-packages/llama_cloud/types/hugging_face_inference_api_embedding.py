# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .hugging_face_inference_api_embedding_token import HuggingFaceInferenceApiEmbeddingToken
from .pooling import Pooling

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class HuggingFaceInferenceApiEmbedding(pydantic.BaseModel):
    model_name: typing.Optional[str]
    embed_batch_size: typing.Optional[int] = pydantic.Field(description="The batch size for embedding calls.")
    num_workers: typing.Optional[int]
    pooling: typing.Optional[Pooling]
    query_instruction: typing.Optional[str]
    text_instruction: typing.Optional[str]
    token: typing.Optional[HuggingFaceInferenceApiEmbeddingToken] = pydantic.Field(
        description="Hugging Face token. Will default to the locally saved token. Pass token=False if you don’t want to send your token to the server."
    )
    timeout: typing.Optional[float]
    headers: typing.Optional[typing.Dict[str, typing.Optional[str]]]
    cookies: typing.Optional[typing.Dict[str, typing.Optional[str]]]
    task: typing.Optional[str]
    class_name: typing.Optional[str]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
