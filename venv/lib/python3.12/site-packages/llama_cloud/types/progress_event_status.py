# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class ProgressEventStatus(str, enum.Enum):
    """
    Current status of the operation
    """

    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ERROR = "error"

    def visit(
        self,
        pending: typing.Callable[[], T_Result],
        in_progress: typing.Callable[[], T_Result],
        completed: typing.Callable[[], T_Result],
        error: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is ProgressEventStatus.PENDING:
            return pending()
        if self is ProgressEventStatus.IN_PROGRESS:
            return in_progress()
        if self is ProgressEventStatus.COMPLETED:
            return completed()
        if self is ProgressEventStatus.ERROR:
            return error()
