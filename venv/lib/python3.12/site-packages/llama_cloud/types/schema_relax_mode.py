# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class SchemaRelaxMode(str, enum.Enum):
    FULL = "FULL"
    TOP_LEVEL = "TOP_LEVEL"
    LEAF = "LEAF"

    def visit(
        self,
        full: typing.Callable[[], T_Result],
        top_level: typing.Callable[[], T_Result],
        leaf: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is SchemaRelaxMode.FULL:
            return full()
        if self is SchemaRelaxMode.TOP_LEVEL:
            return top_level()
        if self is SchemaRelaxMode.LEAF:
            return leaf()
