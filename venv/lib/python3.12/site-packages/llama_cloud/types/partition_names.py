# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class PartitionNames(str, enum.Enum):
    """
    Enum for dataset partition names.
    """

    DATA_SOURCE_ID_PARTITION = "data_source_id_partition"
    PIPELINE_ID_PARTITION = "pipeline_id_partition"
    EVAL_DATASET_ID_PARTITION = "eval_dataset_id_partition"
    FILE_ID_PARTITION = "file_id_partition"
    PIPELINE_FILE_ID_PARTITION = "pipeline_file_id_partition"
    FILE_PARSING_ID_PARTITION = "file_parsing_id_partition"
    EXTRACTION_SCHEMA_ID_PARTITION = "extraction_schema_id_partition"

    def visit(
        self,
        data_source_id_partition: typing.Callable[[], T_Result],
        pipeline_id_partition: typing.Callable[[], T_Result],
        eval_dataset_id_partition: typing.Callable[[], T_Result],
        file_id_partition: typing.Callable[[], T_Result],
        pipeline_file_id_partition: typing.Callable[[], T_Result],
        file_parsing_id_partition: typing.Callable[[], T_Result],
        extraction_schema_id_partition: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is PartitionNames.DATA_SOURCE_ID_PARTITION:
            return data_source_id_partition()
        if self is PartitionNames.PIPELINE_ID_PARTITION:
            return pipeline_id_partition()
        if self is PartitionNames.EVAL_DATASET_ID_PARTITION:
            return eval_dataset_id_partition()
        if self is PartitionNames.FILE_ID_PARTITION:
            return file_id_partition()
        if self is PartitionNames.PIPELINE_FILE_ID_PARTITION:
            return pipeline_file_id_partition()
        if self is PartitionNames.FILE_PARSING_ID_PARTITION:
            return file_parsing_id_partition()
        if self is PartitionNames.EXTRACTION_SCHEMA_ID_PARTITION:
            return extraction_schema_id_partition()
