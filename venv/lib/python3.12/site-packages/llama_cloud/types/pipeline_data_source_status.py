# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class PipelineDataSourceStatus(str, enum.Enum):
    NOT_STARTED = "NOT_STARTED"
    IN_PROGRESS = "IN_PROGRESS"
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    CANCELLED = "CANCELLED"

    def visit(
        self,
        not_started: typing.Callable[[], T_Result],
        in_progress: typing.Callable[[], T_Result],
        success: typing.Callable[[], T_Result],
        error: typing.Callable[[], T_Result],
        cancelled: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is PipelineDataSourceStatus.NOT_STARTED:
            return not_started()
        if self is PipelineDataSourceStatus.IN_PROGRESS:
            return in_progress()
        if self is PipelineDataSourceStatus.SUCCESS:
            return success()
        if self is PipelineDataSourceStatus.ERROR:
            return error()
        if self is PipelineDataSourceStatus.CANCELLED:
            return cancelled()
