# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class FileParsePublic(pydantic.BaseModel):
    created_at: dt.datetime = pydantic.Field(description="The date and time when the file was parsed.")
    status: str = pydantic.Field(description="The status of the parse task.")
    started_at: typing.Optional[dt.datetime]
    ended_at: typing.Optional[dt.datetime]
    input_path: str = pydantic.Field(description="The path to the input file.")
    data_path: str = pydantic.Field(description="The path to the data file.")

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
