# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class VertexEmbeddingMode(str, enum.Enum):
    """
    Copied from llama_index.embeddings.vertex.base.VertexEmbeddingMode
    since importing llama_index.embeddings.vertex.base incurs a lot of memory usage.
    """

    DEFAULT = "default"
    CLASSIFICATION = "classification"
    CLUSTERING = "clustering"
    SIMILARITY = "similarity"
    RETRIEVAL = "retrieval"

    def visit(
        self,
        default: typing.Callable[[], T_Result],
        classification: typing.Callable[[], T_Result],
        clustering: typing.Callable[[], T_Result],
        similarity: typing.Callable[[], T_Result],
        retrieval: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is VertexEmbeddingMode.DEFAULT:
            return default()
        if self is VertexEmbeddingMode.CLASSIFICATION:
            return classification()
        if self is VertexEmbeddingMode.CLUSTERING:
            return clustering()
        if self is VertexEmbeddingMode.SIMILARITY:
            return similarity()
        if self is VertexEmbeddingMode.RETRIEVAL:
            return retrieval()
