# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class ReportEventType(str, enum.Enum):
    LOAD_TEMPLATE = "load_template"
    EXTRACT_PLAN = "extract_plan"
    SUMMARIZE = "summarize"
    FILE_PROCESSING = "file_processing"
    GENERATE_BLOCK = "generate_block"
    EDITING = "editing"

    def visit(
        self,
        load_template: typing.Callable[[], T_Result],
        extract_plan: typing.Callable[[], T_Result],
        summarize: typing.Callable[[], T_Result],
        file_processing: typing.Callable[[], T_Result],
        generate_block: typing.Callable[[], T_Result],
        editing: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is ReportEventType.LOAD_TEMPLATE:
            return load_template()
        if self is ReportEventType.EXTRACT_PLAN:
            return extract_plan()
        if self is ReportEventType.SUMMARIZE:
            return summarize()
        if self is ReportEventType.FILE_PROCESSING:
            return file_processing()
        if self is ReportEventType.GENERATE_BLOCK:
            return generate_block()
        if self is ReportEventType.EDITING:
            return editing()
