# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import typing_extensions

from .data_source_update_dispatcher_config import DataSourceUpdateDispatcherConfig
from .document_ingestion_job_params import DocumentIngestionJobParams
from .l_lama_parse_transform_config import LLamaParseTransformConfig
from .legacy_parse_job_config import LegacyParseJobConfig
from .load_files_job_config import LoadFilesJobConfig
from .parse_job_config import ParseJobConfig
from .pipeline_file_update_dispatcher_config import PipelineFileUpdateDispatcherConfig
from .pipeline_file_updater_config import PipelineFileUpdaterConfig
from .pipeline_managed_ingestion_job_params import PipelineManagedIngestionJobParams


class JobRecordParameters_DataSourceUpdateDispatcher(DataSourceUpdateDispatcherConfig):
    type: typing_extensions.Literal["data_source_update_dispatcher"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class JobRecordParameters_DocumentIngestion(DocumentIngestionJobParams):
    type: typing_extensions.Literal["document_ingestion"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class JobRecordParameters_LegacyParse(LegacyParseJobConfig):
    type: typing_extensions.Literal["legacy_parse"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class JobRecordParameters_LlamaParseTransform(LLamaParseTransformConfig):
    type: typing_extensions.Literal["llama_parse_transform"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class JobRecordParameters_LoadFiles(LoadFilesJobConfig):
    type: typing_extensions.Literal["load_files"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class JobRecordParameters_Parse(ParseJobConfig):
    type: typing_extensions.Literal["parse"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class JobRecordParameters_PipelineFileUpdateDispatcher(PipelineFileUpdateDispatcherConfig):
    type: typing_extensions.Literal["pipeline_file_update_dispatcher"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class JobRecordParameters_PipelineFileUpdater(PipelineFileUpdaterConfig):
    type: typing_extensions.Literal["pipeline_file_updater"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class JobRecordParameters_PipelineManagedIngestion(PipelineManagedIngestionJobParams):
    type: typing_extensions.Literal["pipeline_managed_ingestion"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


JobRecordParameters = typing.Union[
    JobRecordParameters_DataSourceUpdateDispatcher,
    JobRecordParameters_DocumentIngestion,
    JobRecordParameters_LegacyParse,
    JobRecordParameters_LlamaParseTransform,
    JobRecordParameters_LoadFiles,
    JobRecordParameters_Parse,
    JobRecordParameters_PipelineFileUpdateDispatcher,
    JobRecordParameters_PipelineFileUpdater,
    JobRecordParameters_PipelineManagedIngestion,
]
