# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .free_credits_usage import FreeCreditsUsage
from .usage_response_active_alerts_item import UsageResponseActiveAlertsItem

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class UsageResponse(pydantic.BaseModel):
    """
    Response model
    """

    active_free_credits_usage: typing.Optional[typing.List[FreeCreditsUsage]]
    total_users: typing.Optional[int]
    total_indexes: typing.Optional[int]
    total_indexed_pages: typing.Optional[int]
    active_alerts: typing.Optional[typing.List[UsageResponseActiveAlertsItem]]
    current_invoice_total_usd_cents: typing.Optional[int]
    total_extraction_agents: typing.Optional[int]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
