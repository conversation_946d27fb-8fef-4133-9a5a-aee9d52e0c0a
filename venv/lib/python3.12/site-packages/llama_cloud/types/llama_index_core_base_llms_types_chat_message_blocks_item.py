# This file was auto-generated by <PERSON><PERSON> from our API Definition.

from __future__ import annotations

import typing

import typing_extensions

from .audio_block import AudioBlock
from .document_block import DocumentBlock
from .image_block import ImageBlock
from .text_block import TextBlock


class LlamaIndexCoreBaseLlmsTypesChatMessageBlocksItem_Audio(AudioBlock):
    block_type: typing_extensions.Literal["audio"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class LlamaIndexCoreBaseLlmsTypesChatMessageBlocksItem_Document(DocumentBlock):
    block_type: typing_extensions.Literal["document"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class LlamaIndexCoreBaseLlmsTypesChatMessageBlocksItem_Image(ImageBlock):
    block_type: typing_extensions.Literal["image"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class LlamaIndexCoreBaseLlmsTypesChatMessageBlocksItem_Text(TextBlock):
    block_type: typing_extensions.Literal["text"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


LlamaIndexCoreBaseLlmsTypesChatMessageBlocksItem = typing.Union[
    LlamaIndexCoreBaseLlmsTypesChatMessageBlocksItem_Audio,
    LlamaIndexCoreBaseLlmsTypesChatMessageBlocksItem_Document,
    LlamaIndexCoreBaseLlmsTypesChatMessageBlocksItem_Image,
    LlamaIndexCoreBaseLlmsTypesChatMessageBlocksItem_Text,
]
