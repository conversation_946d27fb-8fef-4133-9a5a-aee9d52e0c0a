# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class ObjectType(str, enum.Enum):
    ONE = "1"
    TWO = "2"
    THREE = "3"
    FOUR = "4"
    FIVE = "5"

    def visit(
        self,
        one: typing.Callable[[], T_Result],
        two: typing.Callable[[], T_Result],
        three: typing.Callable[[], T_Result],
        four: typing.Callable[[], T_Result],
        five: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is ObjectType.ONE:
            return one()
        if self is ObjectType.TWO:
            return two()
        if self is ObjectType.THREE:
            return three()
        if self is ObjectType.FOUR:
            return four()
        if self is ObjectType.FIVE:
            return five()
