# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .user_organization_role import UserOrganizationRole

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class UserOrganization(pydantic.BaseModel):
    """
    Schema for a user's membership to an organization.
    """

    id: str = pydantic.Field(description="Unique identifier")
    created_at: typing.Optional[dt.datetime]
    updated_at: typing.Optional[dt.datetime]
    email: typing.Optional[str]
    user_id: typing.Optional[str]
    organization_id: str = pydantic.Field(description="The organization's ID.")
    pending: typing.Optional[bool] = pydantic.Field(
        description="Whether the user's membership is pending account signup."
    )
    invited_by_user_id: typing.Optional[str]
    invited_by_user_email: typing.Optional[str]
    roles: typing.List[UserOrganizationRole] = pydantic.Field(description="The roles of the user in the organization.")

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
