# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class NodeRelationship(str, enum.Enum):
    """
    Node relationships used in `BaseNode` class.

    Attributes:
    SOURCE: The node is the source document.
    PREVIOUS: The node is the previous node in the document.
    NEXT: The node is the next node in the document.
    PARENT: The node is the parent node in the document.
    CHILD: The node is a child node in the document.
    """

    ONE = "1"
    TWO = "2"
    THREE = "3"
    FOUR = "4"
    FIVE = "5"

    def visit(
        self,
        one: typing.Callable[[], T_Result],
        two: typing.Callable[[], T_Result],
        three: typing.Callable[[], T_Result],
        four: typing.Callable[[], T_Result],
        five: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is NodeRelationship.ONE:
            return one()
        if self is NodeRelationship.TWO:
            return two()
        if self is NodeRelationship.THREE:
            return three()
        if self is NodeRelationship.FOUR:
            return four()
        if self is NodeRelationship.FIVE:
            return five()
