# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class PgVectorDistanceMethod(str, enum.Enum):
    """
    Distance methods for PGVector.
    Docs:
    https://github.com/pgvector/pgvector?tab=readme-ov-file#query-options
    """

    L_2 = "l2"
    IP = "ip"
    COSINE = "cosine"
    L_1 = "l1"
    HAMMING = "hamming"
    JACCARD = "jaccard"

    def visit(
        self,
        l_2: typing.Callable[[], T_Result],
        ip: typing.Callable[[], T_Result],
        cosine: typing.Callable[[], T_Result],
        l_1: typing.Callable[[], T_Result],
        hamming: typing.Callable[[], T_Result],
        jaccard: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is PgVectorDistanceMethod.L_2:
            return l_2()
        if self is PgVectorDistanceMethod.IP:
            return ip()
        if self is PgVectorDistanceMethod.COSINE:
            return cosine()
        if self is PgVectorDistanceMethod.L_1:
            return l_1()
        if self is PgVectorDistanceMethod.HAMMING:
            return hamming()
        if self is PgVectorDistanceMethod.JACCARD:
            return jaccard()
