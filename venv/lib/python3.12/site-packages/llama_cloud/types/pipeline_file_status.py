# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class PipelineFileStatus(str, enum.Enum):
    NOT_STARTED = "NOT_STARTED"
    IN_PROGRESS = "IN_PROGRESS"
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    CANCELLED = "CANCELLED"

    def visit(
        self,
        not_started: typing.Callable[[], T_Result],
        in_progress: typing.Callable[[], T_Result],
        success: typing.Callable[[], T_Result],
        error: typing.Callable[[], T_Result],
        cancelled: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is PipelineFileStatus.NOT_STARTED:
            return not_started()
        if self is PipelineFileStatus.IN_PROGRESS:
            return in_progress()
        if self is PipelineFileStatus.SUCCESS:
            return success()
        if self is PipelineFileStatus.ERROR:
            return error()
        if self is PipelineFileStatus.CANCELLED:
            return cancelled()
