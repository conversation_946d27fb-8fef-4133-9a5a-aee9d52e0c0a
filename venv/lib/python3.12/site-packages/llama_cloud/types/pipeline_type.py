# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class PipelineType(str, enum.Enum):
    """
    Enum for representing the type of a pipeline
    """

    PLAYGROUND = "PLAYGROUND"
    MANAGED = "MANAGED"

    def visit(self, playground: typing.Callable[[], T_Result], managed: typing.Callable[[], T_Result]) -> T_Result:
        if self is PipelineType.PLAYGROUND:
            return playground()
        if self is PipelineType.MANAGED:
            return managed()
