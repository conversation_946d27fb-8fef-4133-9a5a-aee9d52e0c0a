# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .text_node_relationships_value import TextNodeRelationshipsValue

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class TextNode(pydantic.BaseModel):
    """
    Provided for backward compatibility.

    Note: we keep the field with the typo "seperator" to maintain backward compatibility for
    serialized objects.
    """

    id: typing.Optional[str] = pydantic.Field(alias="id_", description="Unique ID of the node.")
    embedding: typing.Optional[typing.List[float]]
    extra_info: typing.Optional[typing.Dict[str, typing.Any]] = pydantic.Field(
        description="A flat dictionary of metadata fields"
    )
    excluded_embed_metadata_keys: typing.Optional[typing.List[str]] = pydantic.Field(
        description="Metadata keys that are excluded from text for the embed model."
    )
    excluded_llm_metadata_keys: typing.Optional[typing.List[str]] = pydantic.Field(
        description="Metadata keys that are excluded from text for the LLM."
    )
    relationships: typing.Optional[typing.Dict[str, TextNodeRelationshipsValue]] = pydantic.Field(
        description="A mapping of relationships to other node information."
    )
    metadata_template: typing.Optional[str] = pydantic.Field(
        description="Template for how metadata is formatted, with {key} and {value} placeholders."
    )
    metadata_seperator: typing.Optional[str] = pydantic.Field(
        description="Separator between metadata fields when converting to string."
    )
    text: typing.Optional[str] = pydantic.Field(description="Text content of the node.")
    mimetype: typing.Optional[str] = pydantic.Field(description="MIME type of the node content.")
    start_char_idx: typing.Optional[int]
    end_char_idx: typing.Optional[int]
    text_template: typing.Optional[str] = pydantic.Field(
        description="Template for how text is formatted, with {content} and {metadata_str} placeholders."
    )
    class_name: typing.Optional[str]

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        json_encoders = {dt.datetime: serialize_datetime}
