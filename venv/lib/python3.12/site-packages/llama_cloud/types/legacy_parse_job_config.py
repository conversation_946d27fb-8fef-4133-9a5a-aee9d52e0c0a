# This file was auto-generated by Fern from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class LegacyParseJobConfig(pydantic.BaseModel):
    """
    Configuration for llamaparse job
    """

    custom_metadata: typing.Optional[typing.Dict[str, typing.Any]]
    resource_info: typing.Optional[typing.Dict[str, typing.Any]]
    user_id: str = pydantic.Field(alias="userId", description="The user ID.")
    file_name: str = pydantic.Field(alias="fileName", description="The file name.")
    original_file_name: str = pydantic.Field(alias="originalFileName", description="The original file name.")
    file_key: str = pydantic.Field(alias="fileKey", description="The file key.")
    input_url: typing.Optional[str] = pydantic.Field(alias="inputUrl")
    http_proxy: typing.Optional[str] = pydantic.Field(alias="httpProxy")
    fast_mode: typing.Optional[bool] = pydantic.Field(alias="fastMode")
    lang: str = pydantic.Field(description="The language.")
    template: typing.Optional[str] = pydantic.Field(description="The parsing instruction.")
    pipeline_id: typing.Optional[str] = pydantic.Field(alias="pipelineId")
    output_bucket: typing.Optional[str] = pydantic.Field(alias="outputBucket")
    file_id: typing.Optional[str] = pydantic.Field(alias="fileId")
    full_file_path: typing.Optional[str] = pydantic.Field(alias="fullFilePath")
    from_l_lama_cloud: typing.Optional[bool] = pydantic.Field(
        alias="fromLLamaCloud", description="Whether the file is from LLama cloud."
    )
    skip_diagonal_text: typing.Optional[bool] = pydantic.Field(
        alias="skipDiagonalText", description="Whether to skip diagonal text."
    )
    preserve_layout_alignment_across_pages: typing.Optional[bool] = pydantic.Field(
        alias="preserveLayoutAlignmentAcrossPages", description="Whether to preserve layout alignment across pages."
    )
    invalidate_cache: bool = pydantic.Field(alias="invalidateCache", description="Whether to invalidate the cache.")
    output_pdf_of_document: typing.Optional[bool] = pydantic.Field(alias="outputPDFOfDocument")
    outlined_table_extraction: typing.Optional[bool] = pydantic.Field(alias="outlinedTableExtraction")
    save_images: typing.Optional[bool] = pydantic.Field(alias="saveImages")
    gpt_4_o: typing.Optional[bool] = pydantic.Field(alias="gpt4o", description="Whether to use GPT4o.")
    open_aiapi_key: str = pydantic.Field(alias="openAIAPIKey", description="The OpenAI API key.")
    do_not_unroll_columns: typing.Optional[bool] = pydantic.Field(
        alias="doNotUnrollColumns", description="Whether to unroll columns."
    )
    spread_sheet_extract_sub_tables: typing.Optional[bool] = pydantic.Field(alias="spreadSheetExtractSubTables")
    extract_layout: typing.Optional[bool] = pydantic.Field(alias="extractLayout")
    html_make_all_elements_visible: typing.Optional[bool] = pydantic.Field(alias="htmlMakeAllElementsVisible")
    html_remove_fixed_elements: typing.Optional[bool] = pydantic.Field(alias="htmlRemoveFixedElements")
    html_remove_navigation_elements: typing.Optional[bool] = pydantic.Field(alias="htmlRemoveNavigationElements")
    guess_xlsx_sheet_name: typing.Optional[bool] = pydantic.Field(
        alias="guessXLSXSheetName", description="Whether to guess the XLSX sheet name when generation output xlsx."
    )
    do_not_cache: typing.Optional[bool] = pydantic.Field(alias="doNotCache", description="Whether to cache.")
    page_separator: typing.Optional[str] = pydantic.Field(alias="pageSeparator")
    bounding_box: typing.Optional[str] = pydantic.Field(alias="boundingBox")
    bbox_top: typing.Optional[float] = pydantic.Field(alias="bboxTop")
    bbox_right: typing.Optional[float] = pydantic.Field(alias="bboxRight")
    bbox_bottom: typing.Optional[float] = pydantic.Field(alias="bboxBottom")
    bbox_left: typing.Optional[float] = pydantic.Field(alias="bboxLeft")
    disable_reconstruction: typing.Optional[bool] = pydantic.Field(alias="disableReconstruction")
    target_pages: typing.Optional[str] = pydantic.Field(alias="targetPages")
    multimodal_pipeline: typing.Optional[bool] = pydantic.Field(alias="multimodalPipeline")
    multimodal_model: typing.Optional[str] = pydantic.Field(alias="multimodalModel")
    model: typing.Optional[str]
    vendor_api_key: typing.Optional[str] = pydantic.Field(alias="vendorAPIKey")
    page_prefix: typing.Optional[str] = pydantic.Field(alias="pagePrefix")
    page_suffix: typing.Optional[str] = pydantic.Field(alias="pageSuffix")
    webhook_url: typing.Optional[str] = pydantic.Field(alias="webhookUrl")
    preset: typing.Optional[str]
    take_screenshot: typing.Optional[bool] = pydantic.Field(
        alias="takeScreenshot", description="Force to capture an image of each pages"
    )
    is_formatting_instruction: typing.Optional[bool] = pydantic.Field(
        alias="isFormattingInstruction", description="Allow the parsing instruction to also format the output."
    )
    premium_mode: typing.Optional[bool] = pydantic.Field(
        alias="premiumMode", description="Whether to use premiumMode pipeline."
    )
    continuous_mode: typing.Optional[bool] = pydantic.Field(
        alias="continuousMode", description="Whether to use continuousMode pipeline."
    )
    disable_ocr: typing.Optional[bool] = pydantic.Field(
        alias="disableOcr",
        description="Disable the OCR on the document. LlamaParse will only extract the copyable text from the document",
    )
    disable_image_extraction: typing.Optional[bool] = pydantic.Field(
        alias="disableImageExtraction",
        description="Disable the image extraction from the document. LlamaParse will not extract any image from the document.",
    )
    annotate_links: typing.Optional[bool] = pydantic.Field(
        alias="annotateLinks",
        description="Annotate links in markdown. LlamaParse will try to add links from document into the markdown.",
    )
    adaptive_long_table: typing.Optional[bool] = pydantic.Field(
        alias="adaptiveLongTable",
        description="Adaptive long table. LlamaParse will try to detect long table and adapt the output.",
    )
    compact_markdown_table: typing.Optional[bool] = pydantic.Field(
        alias="compactMarkdownTable",
        description="Compact markdown table. LlamaParse will compact the markdown table to not include too many spaces.",
    )
    input_s_3_path: typing.Optional[str] = pydantic.Field(alias="inputS3Path")
    input_s_3_region: typing.Optional[str] = pydantic.Field(alias="inputS3Region")
    output_s_3_path_prefix: typing.Optional[str] = pydantic.Field(alias="outputS3PathPrefix")
    output_s_3_region: typing.Optional[str] = pydantic.Field(alias="outputS3Region")
    project_id: typing.Optional[str] = pydantic.Field(alias="projectId")
    azure_open_ai_deployment_name: typing.Optional[str] = pydantic.Field(alias="azureOpenAiDeploymentName")
    azure_open_ai_endpoint: typing.Optional[str] = pydantic.Field(alias="azureOpenAiEndpoint")
    azure_open_ai_api_version: typing.Optional[str] = pydantic.Field(alias="azureOpenAiApiVersion")
    azure_open_ai_key: typing.Optional[str] = pydantic.Field(alias="azureOpenAiKey")
    auto_mode: typing.Optional[bool] = pydantic.Field(alias="autoMode", description="Whether to use auto mode.")
    auto_mode_trigger_on_table_in_page: typing.Optional[bool] = pydantic.Field(
        alias="autoModeTriggerOnTableInPage", description="Whether to trigger on table in page."
    )
    auto_mode_trigger_on_image_in_page: typing.Optional[bool] = pydantic.Field(
        alias="autoModeTriggerOnImageInPage", description="Whether to trigger on image in page."
    )
    auto_mode_trigger_on_regexp_in_page: typing.Optional[str] = pydantic.Field(alias="autoModeTriggerOnRegexpInPage")
    auto_mode_trigger_on_text_in_page: typing.Optional[str] = pydantic.Field(alias="autoModeTriggerOnTextInPage")
    auto_mode_configuration_json: typing.Optional[str] = pydantic.Field(alias="autoModeConfigurationJSON")
    structured_output: typing.Optional[bool] = pydantic.Field(
        alias="structuredOutput", description="Whether to use structured output."
    )
    structured_output_json_schema: typing.Optional[str] = pydantic.Field(alias="structuredOutputJSONSchema")
    structured_output_json_schema_name: typing.Optional[str] = pydantic.Field(alias="structuredOutputJSONSchemaName")
    max_pages: typing.Optional[int] = pydantic.Field(alias="maxPages")
    extract_charts: typing.Optional[bool] = pydantic.Field(
        alias="extractCharts", description="Extract charts from the document."
    )
    formatting_instruction: typing.Optional[str] = pydantic.Field(alias="formattingInstruction")
    complemental_formatting_instruction: typing.Optional[str] = pydantic.Field(
        alias="complementalFormattingInstruction"
    )
    content_guideline_instruction: typing.Optional[str] = pydantic.Field(alias="contentGuidelineInstruction")
    job_timeout_in_seconds: typing.Optional[float] = pydantic.Field(alias="jobTimeoutInSeconds")
    job_timeout_extra_time_per_page_in_seconds: typing.Optional[float] = pydantic.Field(
        alias="jobTimeoutExtraTimePerPageInSeconds"
    )
    strict_mode_image_extraction: typing.Optional[bool] = pydantic.Field(
        alias="strictModeImageExtraction",
        description="If true, the job will fail when we are not able to extract an image from a document.",
    )
    strict_mode_image_ocr: typing.Optional[bool] = pydantic.Field(
        alias="strictModeImageOCR",
        description="If true, the job will fail when we are not able to OCR an image from a document.",
    )
    strict_mode_reconstruction: typing.Optional[bool] = pydantic.Field(
        alias="strictModeReconstruction",
        description="If true, the job will fail when we are not able to transform a page to Markdown in a document.",
    )
    strict_mode_buggy_font: typing.Optional[bool] = pydantic.Field(
        alias="strictModeBuggyFont",
        description="If true, the job will fail when we are not able to extract a glyph from the document due to buggy font.",
    )
    ignore_document_elements_for_layout_detection: typing.Optional[bool] = pydantic.Field(
        alias="ignoreDocumentElementsForLayoutDetection",
        description="If true, the job will ignore document element for layout detection, and instead just rely on a visual model, only apply to layout detection.",
    )
    output_tables_as_html: typing.Optional[bool] = pydantic.Field(
        alias="outputTablesAsHTML",
        description="If true, the job will output tables as HTML in the markdown output, useful for merged cells.",
    )
    parse_mode: typing.Optional[str] = pydantic.Field(alias="parseMode")
    system_prompt: typing.Optional[str] = pydantic.Field(alias="systemPrompt")
    system_prompt_append: typing.Optional[str] = pydantic.Field(alias="systemPromptAppend")
    user_prompt: typing.Optional[str] = pydantic.Field(alias="userPrompt")

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True
        json_encoders = {dt.datetime: serialize_datetime}
