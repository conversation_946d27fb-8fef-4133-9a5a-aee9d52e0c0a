# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class JobNameMapping(str, enum.Enum):
    """
    Enum for mapping original job names to readable names.
    """

    MANAGED_INGESTION = "MANAGED_INGESTION"
    DATA_SOURCE = "DATA_SOURCE"
    FILES_UPDATE = "FILES_UPDATE"
    FILE_UPDATER = "FILE_UPDATER"
    PARSE = "PARSE"
    TRANSFORM = "TRANSFORM"
    INGESTION = "INGESTION"
    METADATA_UPDATE = "METADATA_UPDATE"

    def visit(
        self,
        managed_ingestion: typing.Callable[[], T_Result],
        data_source: typing.Callable[[], T_Result],
        files_update: typing.Callable[[], T_Result],
        file_updater: typing.Callable[[], T_Result],
        parse: typing.Callable[[], T_Result],
        transform: typing.Callable[[], T_Result],
        ingestion: typing.Callable[[], T_Result],
        metadata_update: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is JobNameMapping.MANAGED_INGESTION:
            return managed_ingestion()
        if self is JobNameMapping.DATA_SOURCE:
            return data_source()
        if self is JobNameMapping.FILES_UPDATE:
            return files_update()
        if self is JobNameMapping.FILE_UPDATER:
            return file_updater()
        if self is JobNameMapping.PARSE:
            return parse()
        if self is JobNameMapping.TRANSFORM:
            return transform()
        if self is JobNameMapping.INGESTION:
            return ingestion()
        if self is JobNameMapping.METADATA_UPDATE:
            return metadata_update()
