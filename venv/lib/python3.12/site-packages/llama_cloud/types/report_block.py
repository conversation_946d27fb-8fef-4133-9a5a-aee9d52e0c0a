# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import datetime as dt
import typing

from ..core.datetime_utils import serialize_datetime
from .text_node_with_score import TextNodeWithScore

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore


class ReportBlock(pydantic.BaseModel):
    idx: int = pydantic.Field(description="The index of the block")
    template: str = pydantic.Field(description="The content of the block")
    requires_human_review: typing.Optional[bool] = pydantic.Field(description="Whether the block requires human review")
    sources: typing.Optional[typing.List[TextNodeWithScore]] = pydantic.Field(description="The sources for the block")

    def json(self, **kwargs: typing.Any) -> str:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().json(**kwargs_with_defaults)

    def dict(self, **kwargs: typing.Any) -> typing.Dict[str, typing.Any]:
        kwargs_with_defaults: typing.Any = {"by_alias": True, "exclude_unset": True, **kwargs}
        return super().dict(**kwargs_with_defaults)

    class Config:
        frozen = True
        smart_union = True
        json_encoders = {dt.datetime: serialize_datetime}
