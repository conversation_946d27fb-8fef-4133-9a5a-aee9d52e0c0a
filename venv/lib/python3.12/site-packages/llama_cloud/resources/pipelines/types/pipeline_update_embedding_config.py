# This file was auto-generated by Fern from our API Definition.

from __future__ import annotations

import typing

import typing_extensions

from ....types.azure_open_ai_embedding_config import AzureOpenAiEmbeddingConfig
from ....types.bedrock_embedding_config import BedrockEmbeddingConfig
from ....types.cohere_embedding_config import CohereEmbeddingConfig
from ....types.gemini_embedding_config import GeminiEmbeddingConfig
from ....types.hugging_face_inference_api_embedding_config import HuggingFaceInferenceApiEmbeddingConfig
from ....types.open_ai_embedding_config import OpenAiEmbeddingConfig
from ....types.vertex_ai_embedding_config import VertexAiEmbeddingConfig


class PipelineUpdateEmbeddingConfig_AzureEmbedding(AzureOpenAiEmbeddingConfig):
    type: typing_extensions.Literal["AZURE_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class PipelineUpdateEmbeddingConfig_BedrockEmbedding(BedrockEmbeddingConfig):
    type: typing_extensions.Literal["BEDROCK_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class PipelineUpdateEmbeddingConfig_CohereEmbedding(CohereEmbeddingConfig):
    type: typing_extensions.Literal["COHERE_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class PipelineUpdateEmbeddingConfig_GeminiEmbedding(GeminiEmbeddingConfig):
    type: typing_extensions.Literal["GEMINI_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class PipelineUpdateEmbeddingConfig_HuggingfaceApiEmbedding(HuggingFaceInferenceApiEmbeddingConfig):
    type: typing_extensions.Literal["HUGGINGFACE_API_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class PipelineUpdateEmbeddingConfig_OpenaiEmbedding(OpenAiEmbeddingConfig):
    type: typing_extensions.Literal["OPENAI_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


class PipelineUpdateEmbeddingConfig_VertexaiEmbedding(VertexAiEmbeddingConfig):
    type: typing_extensions.Literal["VERTEXAI_EMBEDDING"]

    class Config:
        frozen = True
        smart_union = True
        allow_population_by_field_name = True


PipelineUpdateEmbeddingConfig = typing.Union[
    PipelineUpdateEmbeddingConfig_AzureEmbedding,
    PipelineUpdateEmbeddingConfig_BedrockEmbedding,
    PipelineUpdateEmbeddingConfig_CohereEmbedding,
    PipelineUpdateEmbeddingConfig_GeminiEmbedding,
    PipelineUpdateEmbeddingConfig_HuggingfaceApiEmbedding,
    PipelineUpdateEmbeddingConfig_OpenaiEmbedding,
    PipelineUpdateEmbeddingConfig_VertexaiEmbedding,
]
