# This file was auto-generated by Fern from our API Definition.

import typing

from ....types.cloud_azure_ai_search_vector_store import CloudAzureAiSearchVectorStore
from ....types.cloud_milvus_vector_store import CloudMilvusVectorStore
from ....types.cloud_mongo_db_atlas_vector_search import CloudMongoDbAtlasVectorSearch
from ....types.cloud_pinecone_vector_store import CloudPineconeVectorStore
from ....types.cloud_postgres_vector_store import CloudPostgresVectorStore
from ....types.cloud_qdrant_vector_store import CloudQdrantVectorStore

DataSinkUpdateComponent = typing.Union[
    typing.Dict[str, typing.Any],
    CloudPineconeVectorStore,
    CloudPostgresVectorStore,
    CloudQdrantVectorStore,
    CloudAzureAiSearchVectorStore,
    CloudMongoDbAtlasVectorSearch,
    CloudMilvusVectorStore,
]
