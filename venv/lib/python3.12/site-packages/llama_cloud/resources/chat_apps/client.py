# This file was auto-generated by <PERSON><PERSON> from our API Definition.

import typing
import urllib.parse
from json.decoder import <PERSON><PERSON><PERSON>ecodeError

from ...core.api_error import ApiError
from ...core.client_wrapper import AsyncClientWrapper, SyncClientWrapper
from ...core.jsonable_encoder import jsonable_encoder
from ...core.remove_none_from_dict import remove_none_from_dict
from ...errors.unprocessable_entity_error import UnprocessableEntityError
from ...types.chat_app import ChatApp
from ...types.chat_app_response import ChatAppResponse
from ...types.http_validation_error import HttpValidationError
from ...types.input_message import InputMessage
from ...types.llm_parameters import LlmParameters
from ...types.preset_composite_retrieval_params import PresetCompositeRetrievalParams

try:
    import pydantic
    if pydantic.__version__.startswith("1."):
        raise ImportError
    import pydantic.v1 as pydantic  # type: ignore
except ImportError:
    import pydantic  # type: ignore

# this is used as the default value for optional parameters
OMIT = typing.cast(typing.Any, ...)


class ChatAppsClient:
    def __init__(self, *, client_wrapper: SyncClientWrapper):
        self._client_wrapper = client_wrapper

    def get_chat_apps_api_v_1_apps_get(
        self, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> typing.List[ChatAppResponse]:
        """
        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.chat_apps.get_chat_apps_api_v_1_apps_get()
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/apps"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[ChatAppResponse], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def create_chat_app_api_v_1_apps_post(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        name: str,
        retriever_id: str,
        llm_config: LlmParameters,
        retrieval_config: PresetCompositeRetrievalParams,
    ) -> ChatApp:
        """
        Create a new chat app.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - name: str. Name of the chat app

            - retriever_id: str. ID of the retriever to use for the chat app

            - llm_config: LlmParameters. Configuration for the LLM model to use for the chat app

            - retrieval_config: PresetCompositeRetrievalParams. Configuration for the retrieval model to use for the chat app
        ---
        from llama_cloud import (
            CompositeRetrievalMode,
            LlmParameters,
            PresetCompositeRetrievalParams,
            ReRankConfig,
            ReRankerType,
            SupportedLlmModelNames,
        )
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.chat_apps.create_chat_app_api_v_1_apps_post(
            name="string",
            retriever_id="string",
            llm_config=LlmParameters(
                model_name=SupportedLlmModelNames.GPT_4_O,
            ),
            retrieval_config=PresetCompositeRetrievalParams(
                mode=CompositeRetrievalMode.ROUTING,
                rerank_config=ReRankConfig(
                    type=ReRankerType.SYSTEM_DEFAULT,
                ),
            ),
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/apps"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(
                {
                    "name": name,
                    "retriever_id": retriever_id,
                    "llm_config": llm_config,
                    "retrieval_config": retrieval_config,
                }
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ChatApp, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def get_chat_app(self, id: str) -> ChatApp:
        """
        Get a chat app by ID.

        Parameters:
            - id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.chat_apps.get_chat_app(
            id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/apps/{id}"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ChatApp, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def update_chat_app(
        self,
        id: str,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        name: typing.Optional[str] = OMIT,
        llm_config: typing.Optional[LlmParameters] = OMIT,
        retrieval_config: typing.Optional[PresetCompositeRetrievalParams] = OMIT,
    ) -> ChatApp:
        """
        Update a chat app.

        Parameters:
            - id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - name: typing.Optional[str].

            - llm_config: typing.Optional[LlmParameters].

            - retrieval_config: typing.Optional[PresetCompositeRetrievalParams].
        ---
        from llama_cloud import (
            CompositeRetrievalMode,
            LlmParameters,
            PresetCompositeRetrievalParams,
            ReRankConfig,
            ReRankerType,
            SupportedLlmModelNames,
        )
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.chat_apps.update_chat_app(
            id="string",
            llm_config=LlmParameters(
                model_name=SupportedLlmModelNames.GPT_4_O,
            ),
            retrieval_config=PresetCompositeRetrievalParams(
                mode=CompositeRetrievalMode.ROUTING,
                rerank_config=ReRankConfig(
                    type=ReRankerType.SYSTEM_DEFAULT,
                ),
            ),
        )
        """
        _request: typing.Dict[str, typing.Any] = {}
        if name is not OMIT:
            _request["name"] = name
        if llm_config is not OMIT:
            _request["llm_config"] = llm_config
        if retrieval_config is not OMIT:
            _request["retrieval_config"] = retrieval_config
        _response = self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/apps/{id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ChatApp, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def delete_chat_app(self, id: str) -> typing.Any:
        """
        Parameters:
            - id: str.
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.chat_apps.delete_chat_app(
            id="string",
        )
        """
        _response = self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/apps/{id}"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Any, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    def chat_with_chat_app(self, id: str, *, messages: typing.Optional[typing.List[InputMessage]] = OMIT) -> typing.Any:
        """
        Chat with a chat app.

        Parameters:
            - id: str.

            - messages: typing.Optional[typing.List[InputMessage]].
        ---
        from llama_cloud.client import LlamaCloud

        client = LlamaCloud(
            token="YOUR_TOKEN",
        )
        client.chat_apps.chat_with_chat_app(
            id="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {}
        if messages is not OMIT:
            _request["messages"] = messages
        _response = self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/apps/{id}/chat"),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Any, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)


class AsyncChatAppsClient:
    def __init__(self, *, client_wrapper: AsyncClientWrapper):
        self._client_wrapper = client_wrapper

    async def get_chat_apps_api_v_1_apps_get(
        self, *, project_id: typing.Optional[str] = None, organization_id: typing.Optional[str] = None
    ) -> typing.List[ChatAppResponse]:
        """
        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.chat_apps.get_chat_apps_api_v_1_apps_get()
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/apps"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.List[ChatAppResponse], _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def create_chat_app_api_v_1_apps_post(
        self,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        name: str,
        retriever_id: str,
        llm_config: LlmParameters,
        retrieval_config: PresetCompositeRetrievalParams,
    ) -> ChatApp:
        """
        Create a new chat app.

        Parameters:
            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - name: str. Name of the chat app

            - retriever_id: str. ID of the retriever to use for the chat app

            - llm_config: LlmParameters. Configuration for the LLM model to use for the chat app

            - retrieval_config: PresetCompositeRetrievalParams. Configuration for the retrieval model to use for the chat app
        ---
        from llama_cloud import (
            CompositeRetrievalMode,
            LlmParameters,
            PresetCompositeRetrievalParams,
            ReRankConfig,
            ReRankerType,
            SupportedLlmModelNames,
        )
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.chat_apps.create_chat_app_api_v_1_apps_post(
            name="string",
            retriever_id="string",
            llm_config=LlmParameters(
                model_name=SupportedLlmModelNames.GPT_4_O,
            ),
            retrieval_config=PresetCompositeRetrievalParams(
                mode=CompositeRetrievalMode.ROUTING,
                rerank_config=ReRankConfig(
                    type=ReRankerType.SYSTEM_DEFAULT,
                ),
            ),
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", "api/v1/apps"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(
                {
                    "name": name,
                    "retriever_id": retriever_id,
                    "llm_config": llm_config,
                    "retrieval_config": retrieval_config,
                }
            ),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ChatApp, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def get_chat_app(self, id: str) -> ChatApp:
        """
        Get a chat app by ID.

        Parameters:
            - id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.chat_apps.get_chat_app(
            id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "GET",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/apps/{id}"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ChatApp, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def update_chat_app(
        self,
        id: str,
        *,
        project_id: typing.Optional[str] = None,
        organization_id: typing.Optional[str] = None,
        name: typing.Optional[str] = OMIT,
        llm_config: typing.Optional[LlmParameters] = OMIT,
        retrieval_config: typing.Optional[PresetCompositeRetrievalParams] = OMIT,
    ) -> ChatApp:
        """
        Update a chat app.

        Parameters:
            - id: str.

            - project_id: typing.Optional[str].

            - organization_id: typing.Optional[str].

            - name: typing.Optional[str].

            - llm_config: typing.Optional[LlmParameters].

            - retrieval_config: typing.Optional[PresetCompositeRetrievalParams].
        ---
        from llama_cloud import (
            CompositeRetrievalMode,
            LlmParameters,
            PresetCompositeRetrievalParams,
            ReRankConfig,
            ReRankerType,
            SupportedLlmModelNames,
        )
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.chat_apps.update_chat_app(
            id="string",
            llm_config=LlmParameters(
                model_name=SupportedLlmModelNames.GPT_4_O,
            ),
            retrieval_config=PresetCompositeRetrievalParams(
                mode=CompositeRetrievalMode.ROUTING,
                rerank_config=ReRankConfig(
                    type=ReRankerType.SYSTEM_DEFAULT,
                ),
            ),
        )
        """
        _request: typing.Dict[str, typing.Any] = {}
        if name is not OMIT:
            _request["name"] = name
        if llm_config is not OMIT:
            _request["llm_config"] = llm_config
        if retrieval_config is not OMIT:
            _request["retrieval_config"] = retrieval_config
        _response = await self._client_wrapper.httpx_client.request(
            "PUT",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/apps/{id}"),
            params=remove_none_from_dict({"project_id": project_id, "organization_id": organization_id}),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(ChatApp, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def delete_chat_app(self, id: str) -> typing.Any:
        """
        Parameters:
            - id: str.
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.chat_apps.delete_chat_app(
            id="string",
        )
        """
        _response = await self._client_wrapper.httpx_client.request(
            "DELETE",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/apps/{id}"),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Any, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)

    async def chat_with_chat_app(
        self, id: str, *, messages: typing.Optional[typing.List[InputMessage]] = OMIT
    ) -> typing.Any:
        """
        Chat with a chat app.

        Parameters:
            - id: str.

            - messages: typing.Optional[typing.List[InputMessage]].
        ---
        from llama_cloud.client import AsyncLlamaCloud

        client = AsyncLlamaCloud(
            token="YOUR_TOKEN",
        )
        await client.chat_apps.chat_with_chat_app(
            id="string",
        )
        """
        _request: typing.Dict[str, typing.Any] = {}
        if messages is not OMIT:
            _request["messages"] = messages
        _response = await self._client_wrapper.httpx_client.request(
            "POST",
            urllib.parse.urljoin(f"{self._client_wrapper.get_base_url()}/", f"api/v1/apps/{id}/chat"),
            json=jsonable_encoder(_request),
            headers=self._client_wrapper.get_headers(),
            timeout=60,
        )
        if 200 <= _response.status_code < 300:
            return pydantic.parse_obj_as(typing.Any, _response.json())  # type: ignore
        if _response.status_code == 422:
            raise UnprocessableEntityError(pydantic.parse_obj_as(HttpValidationError, _response.json()))  # type: ignore
        try:
            _response_json = _response.json()
        except JSONDecodeError:
            raise ApiError(status_code=_response.status_code, body=_response.text)
        raise ApiError(status_code=_response.status_code, body=_response_json)
