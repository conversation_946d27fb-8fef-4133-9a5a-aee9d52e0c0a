# This file was auto-generated by <PERSON>rn from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class UpdateReportPlanApiV1ReportsReportIdPlanPatchRequestAction(str, enum.Enum):
    APPROVE = "approve"
    REJECT = "reject"
    EDIT = "edit"

    def visit(
        self,
        approve: typing.Callable[[], T_Result],
        reject: typing.Callable[[], T_Result],
        edit: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is UpdateReportPlanApiV1ReportsReportIdPlanPatchRequestAction.APPROVE:
            return approve()
        if self is UpdateReportPlanApiV1ReportsReportIdPlanPatchRequestAction.REJECT:
            return reject()
        if self is UpdateReportPlanApiV1ReportsReportIdPlanPatchRequestAction.EDIT:
            return edit()
