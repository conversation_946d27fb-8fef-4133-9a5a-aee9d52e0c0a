{"file_info": {"original_filename": "test.txt", "file_path": "/data/BlackTie /llamaindex/input_documents/test.txt", "file_size_bytes": 146, "file_extension": ".txt", "processed_at": "2025-06-12T15:44:49.486478"}, "content": {"extracted_text": "This is a test document for the LlamaIndex processing system. It contains sample text to verify that the document processor is working correctly.\n", "text_length": 146, "word_count": 23}, "metadata": {"file_path": "/data/BlackTie /llamaindex/input_documents/test.txt", "file_name": "test.txt", "file_type": "text/plain", "file_size": 146, "creation_date": "2025-06-12", "last_modified_date": "2025-06-12"}, "processing_info": {"processor": "LlamaParse + LlamaIndex", "extraction_method": "text_only", "success": true}}