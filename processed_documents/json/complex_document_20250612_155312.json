{"file_info": {"original_filename": "complex_document.txt", "file_path": "/data/BlackTie /llamaindex/input_documents/complex_document.txt", "file_size_bytes": 2171, "file_extension": ".txt", "processed_at": "2025-06-12T15:53:12.225063"}, "content": {"extracted_text": "# Complex Document Processing Test\n\n## Executive Summary\nThis document serves as a comprehensive test for the LlamaIndex document processing system. It contains various types of content including headers, paragraphs, lists, and structured data to verify the system's text extraction capabilities.\n\n## Introduction\nThe document processing backend utilizes LlamaIndex and LlamaParse to extract text from various file formats including PDF, TXT, MD, and DOCX files. The system processes documents and outputs structured JSON data containing the extracted text, metadata, and processing information.\n\n## Key Features\n1. **Multi-format Support**: Handles PDF, text, markdown, and Word documents\n2. **Structured Output**: Generates clean JSON with organized data\n3. **API Integration**: RESTful API for on-demand processing\n4. **Batch Processing**: Concurrent processing of multiple files\n5. **Error Handling**: Robust error management and logging\n\n## Technical Architecture\nThe system consists of several components:\n- FastAPI server for API endpoints\n- Document processor using LlamaIndex/LlamaParse\n- Configuration management\n- Utility functions for file handling\n- Structured JSON output generation\n\n## Processing Workflow\n1. File detection and validation\n2. Format-specific processing (LlamaParse for PDFs, SimpleDirectoryReader for text)\n3. Text extraction and cleaning\n4. Metadata collection\n5. JSON structure creation\n6. Result storage and indexing\n\n## Performance Metrics\n- Processing speed: Variable based on file size and complexity\n- Supported file sizes: Up to 50MB per file\n- Concurrent processing: Up to 4 files simultaneously\n- Output format: Structured JSON with metadata\n\n## Conclusion\nThis document processing system provides a robust solution for extracting and structuring text content from various document formats. The integration of LlamaIndex and LlamaParse ensures high-quality text extraction with comprehensive metadata preservation.\n\n## Appendix\nAdditional technical details and configuration options are available in the system documentation. The API provides comprehensive endpoints for file management, processing control, and result retrieval.\n", "text_length": 2171, "word_count": 294}, "metadata": {"file_path": "/data/BlackTie /llamaindex/input_documents/complex_document.txt", "file_name": "complex_document.txt", "file_type": "text/plain", "file_size": 2171, "creation_date": "2025-06-12", "last_modified_date": "2025-06-12"}, "processing_info": {"processor": "LlamaParse + LlamaIndex", "extraction_method": "text_only", "success": true}}