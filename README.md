# Document Processing Backend

A powerful backend system using **LlamaIndex** and **LlamaParse** to automatically process PDF and text files, extracting structured data in JSON format.

## 🚀 Features

- **Multi-format Support**: Process PDF, TXT, MD, and DOCX files
- **Structured JSON Output**: Clean, organized data extraction
- **On-demand Processing**: REST API for flexible file processing
- **Batch Processing**: Handle multiple files simultaneously
- **Real-time Monitoring**: Track processing status and results
- **Error Handling**: Robust error handling with detailed logging

## 📁 Directory Structure

```
/
├── input_documents/          # Drop your files here
├── processed_documents/      # Processed results
│   ├── json/                # JSON output files
│   ├── metadata/            # File metadata
│   └── errors/              # Error logs
├── logs/                    # Application logs
├── main.py                  # FastAPI server
├── document_processor.py    # Core processing logic
├── config.py               # Configuration settings
├── utils.py                # Utility functions
├── requirements.txt        # Dependencies
└── README.md              # This file
```

## 🛠️ Setup Instructions

### 1. Install Dependencies

```bash
# Activate your virtual environment
source venv/bin/activate

# Install required packages
pip install -r requirements.txt
```

### 2. Configure API Key

The LlamaParse API key is already configured in `config.py`. If you need to change it:

```python
# In config.py
LLAMA_CLOUD_API_KEY = "your-api-key-here"
```

### 3. Create Directories

```bash
# Run configuration to create necessary directories
python config.py
```

## 🎯 Usage

### Method 1: Start the Server

```bash
# Start the FastAPI server
python main.py
```

The server will start at `http://127.0.0.1:8000`

### Method 2: Direct Processing

```bash
# Process files directly
python document_processor.py
```

## 🌐 API Endpoints

Once the server is running, you can access these endpoints:

### Main Endpoints

- **`GET /`** - Service information and available endpoints
- **`GET /health`** - Health check
- **`GET /files`** - List all files in input directory
- **`POST /process`** - Process all files in input directory
- **`POST /process/{filename}`** - Process a specific file
- **`GET /results`** - List all processing results
- **`DELETE /results`** - Clear all results

### Interactive API Documentation

Visit `http://127.0.0.1:8000/docs` for interactive API documentation.

## 📝 How to Use

### Step 1: Add Files
Place your PDF or text files in the `input_documents/` folder:

```bash
cp your-document.pdf input_documents/
cp your-text-file.txt input_documents/
```

### Step 2: Process Files

**Option A: Process All Files**
```bash
curl -X POST http://127.0.0.1:8000/process
```

**Option B: Process Specific File**
```bash
curl -X POST http://127.0.0.1:8000/process/your-document.pdf
```

**Option C: Use the Web Interface**
Go to `http://127.0.0.1:8000/docs` and use the interactive interface.

### Step 3: Get Results
Processed files will be saved as JSON in `processed_documents/json/` with this structure:

```json
{
  "file_info": {
    "original_filename": "document.pdf",
    "file_path": "/path/to/document.pdf",
    "file_size_bytes": 1024000,
    "file_extension": ".pdf",
    "processed_at": "2024-01-15T10:30:00"
  },
  "content": {
    "extracted_text": "Full text content here...",
    "text_length": 5000,
    "word_count": 850
  },
  "metadata": {
    "page_count": 10,
    "author": "Document Author"
  },
  "processing_info": {
    "processor": "LlamaParse + LlamaIndex",
    "extraction_method": "text_only",
    "success": true
  }
}
```

## 🔧 Configuration

Edit `config.py` to customize:

- **File size limits**: `MAX_FILE_SIZE_MB`
- **Supported formats**: `SUPPORTED_EXTENSIONS`
- **Processing settings**: `LLAMAPARSE_CONFIG`
- **Server settings**: `SERVER_HOST`, `SERVER_PORT`

## 📊 Monitoring

### Check Processing Status
```bash
curl http://127.0.0.1:8000/files
```

### View Results
```bash
curl http://127.0.0.1:8000/results
```

### Health Check
```bash
curl http://127.0.0.1:8000/health
```

## 🐛 Troubleshooting

### Common Issues

1. **API Key Error**
   - Verify your LlamaParse API key in `config.py`
   - Check if you have sufficient API credits

2. **File Not Processing**
   - Ensure file is in `input_documents/` folder
   - Check if file format is supported
   - Verify file size is under the limit

3. **Server Won't Start**
   - Check if port 8000 is available
   - Ensure all dependencies are installed
   - Check the logs for error messages

### Logs

Check application logs in the `logs/` directory for detailed error information.

## 📚 Supported File Types

- **PDF**: `.pdf` (using LlamaParse)
- **Text**: `.txt` (using SimpleDirectoryReader)
- **Markdown**: `.md` (using SimpleDirectoryReader)
- **Word**: `.docx` (using SimpleDirectoryReader)

## 🔄 Processing Flow

1. **File Detection**: System scans `input_documents/` folder
2. **Validation**: Checks file type and size
3. **Processing**: Uses LlamaParse for PDFs, SimpleDirectoryReader for text files
4. **Extraction**: Extracts text content and metadata
5. **Structuring**: Creates structured JSON output
6. **Storage**: Saves results to `processed_documents/json/`

## 🚀 Quick Start Example

```bash
# 1. Start the server
python main.py

# 2. In another terminal, add a test file
echo "This is a test document." > input_documents/test.txt

# 3. Process the file
curl -X POST http://127.0.0.1:8000/process

# 4. Check results
ls processed_documents/json/
```

## 📞 Support

For issues or questions:
1. Check the logs in `logs/` directory
2. Verify your API key and credits
3. Ensure all dependencies are properly installed
4. Check the interactive API docs at `/docs`

---

**Built with ❤️ using LlamaIndex and LlamaParse**
