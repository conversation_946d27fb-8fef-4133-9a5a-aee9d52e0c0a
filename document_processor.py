"""
Document processor using LlamaIndex and LlamaParse.
"""
import json
import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional
import nest_asyncio

from llama_parse import LlamaParse
from llama_index.core import SimpleDire<PERSON><PERSON><PERSON><PERSON>er, Document

from config import (
    LLAMA_CLOUD_API_KEY, 
    LLAMAPARSE_CONFIG, 
    OUTPUT_DIR, 
    SUPPORTED_EXTENSIONS,
    MAX_FILE_SIZE_MB
)

# Apply nest_asyncio to handle async in Jupyter/existing event loops
nest_asyncio.apply()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DocumentProcessor:
    """Main document processing class."""
    
    def __init__(self):
        """Initialize the document processor."""
        self.parser = LlamaParse(
            api_key=LLAMA_CLOUD_API_KEY,
            **LLAMAPARSE_CONFIG
        )
        
    def _validate_file(self, file_path: Path) -> bool:
        """Validate if file can be processed."""
        # Check file extension
        if file_path.suffix.lower() not in SUPPORTED_EXTENSIONS:
            logger.warning(f"Unsupported file type: {file_path.suffix}")
            return False
            
        # Check file size
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        if file_size_mb > MAX_FILE_SIZE_MB:
            logger.warning(f"File too large: {file_size_mb:.2f}MB > {MAX_FILE_SIZE_MB}MB")
            return False
            
        return True
    
    def _create_output_structure(self, file_path: Path, content: str, metadata: Dict) -> Dict[str, Any]:
        """Create structured JSON output."""
        return {
            "file_info": {
                "original_filename": file_path.name,
                "file_path": str(file_path),
                "file_size_bytes": file_path.stat().st_size,
                "file_extension": file_path.suffix.lower(),
                "processed_at": datetime.now().isoformat()
            },
            "content": {
                "extracted_text": content,
                "text_length": len(content),
                "word_count": len(content.split()) if content else 0
            },
            "metadata": metadata,
            "processing_info": {
                "processor": "LlamaParse + LlamaIndex",
                "extraction_method": "text_only",
                "success": True
            }
        }
    
    async def process_single_file(self, file_path: Path) -> Dict[str, Any]:
        """Process a single file and return structured JSON."""
        logger.info(f"Processing file: {file_path.name}")
        
        try:
            # Validate file
            if not self._validate_file(file_path):
                raise ValueError(f"File validation failed for {file_path.name}")
            
            # Process based on file type
            if file_path.suffix.lower() == '.pdf':
                # Use LlamaParse for PDF files
                documents = await self.parser.aload_data(str(file_path))
            else:
                # Use SimpleDirectoryReader for text files
                reader = SimpleDirectoryReader(input_files=[str(file_path)])
                documents = reader.load_data()
            
            # Extract content and metadata
            if documents:
                # Combine all document content
                combined_content = "\n\n".join([doc.text for doc in documents if doc.text])
                
                # Extract metadata from first document
                metadata = documents[0].metadata if documents[0].metadata else {}
                
                # Create structured output
                result = self._create_output_structure(file_path, combined_content, metadata)
                
                logger.info(f"Successfully processed: {file_path.name}")
                return result
            else:
                raise ValueError("No content extracted from file")
                
        except Exception as e:
            logger.error(f"Error processing {file_path.name}: {str(e)}")
            return {
                "file_info": {
                    "original_filename": file_path.name,
                    "file_path": str(file_path),
                    "processed_at": datetime.now().isoformat()
                },
                "content": {
                    "extracted_text": "",
                    "text_length": 0,
                    "word_count": 0
                },
                "metadata": {},
                "processing_info": {
                    "processor": "LlamaParse + LlamaIndex",
                    "extraction_method": "text_only",
                    "success": False,
                    "error": str(e)
                }
            }
    
    async def process_multiple_files(self, file_paths: List[Path]) -> List[Dict[str, Any]]:
        """Process multiple files concurrently."""
        logger.info(f"Processing {len(file_paths)} files...")
        
        # Process files concurrently
        tasks = [self.process_single_file(file_path) for file_path in file_paths]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Exception processing {file_paths[i].name}: {str(result)}")
                processed_results.append({
                    "file_info": {
                        "original_filename": file_paths[i].name,
                        "processed_at": datetime.now().isoformat()
                    },
                    "processing_info": {
                        "success": False,
                        "error": str(result)
                    }
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    def save_results(self, results: List[Dict[str, Any]]) -> List[Path]:
        """Save processing results to JSON files."""
        saved_files = []
        
        for result in results:
            filename = result["file_info"]["original_filename"]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Create output filename
            base_name = Path(filename).stem
            output_filename = f"{base_name}_{timestamp}.json"
            output_path = OUTPUT_DIR / "json" / output_filename
            
            # Save JSON file
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                saved_files.append(output_path)
                logger.info(f"Saved result to: {output_path}")
                
            except Exception as e:
                logger.error(f"Error saving result for {filename}: {str(e)}")
        
        return saved_files


# Convenience function for direct usage
async def process_files_in_directory(input_dir: Path) -> List[Dict[str, Any]]:
    """Process all supported files in a directory."""
    processor = DocumentProcessor()
    
    # Find all supported files
    file_paths = []
    for ext in SUPPORTED_EXTENSIONS:
        file_paths.extend(input_dir.glob(f"*{ext}"))
    
    if not file_paths:
        logger.info("No supported files found in input directory")
        return []
    
    # Process files
    results = await processor.process_multiple_files(file_paths)
    
    # Save results
    processor.save_results(results)
    
    return results


if __name__ == "__main__":
    # Test the processor
    from config import INPUT_DIR, ensure_directories
    
    ensure_directories()
    
    async def test_processor():
        results = await process_files_in_directory(INPUT_DIR)
        print(f"Processed {len(results)} files")
        for result in results:
            print(f"- {result['file_info']['original_filename']}: {result['processing_info']['success']}")
    
    asyncio.run(test_processor())
