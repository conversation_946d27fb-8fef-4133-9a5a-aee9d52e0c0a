"""
Configuration settings for the document processing backend.
"""
import os
from pathlib import Path

# API Configuration
LLAMA_CLOUD_API_KEY = "llx-7sVL62nV9wqGL90fCA77vw3TOckaEfLijQXoPQsNJf1tw6Sd"

# Directory Configuration
BASE_DIR = Path(__file__).parent
INPUT_DIR = BASE_DIR / "input_documents"
OUTPUT_DIR = BASE_DIR / "processed_documents"
LOGS_DIR = BASE_DIR / "logs"

# Supported file extensions
SUPPORTED_EXTENSIONS = {'.pdf', '.txt', '.md', '.docx'}

# LlamaParse Configuration
LLAMAPARSE_CONFIG = {
    "result_type": "text",  # We want text extraction
    "verbose": True,
    "num_workers": 4,
    "language": "en"
}

# Server Configuration
SERVER_HOST = "127.0.0.1"
SERVER_PORT = 8000

# Processing Configuration
MAX_FILE_SIZE_MB = 50  # Maximum file size in MB
BATCH_SIZE = 5  # Number of files to process in parallel

# Create directories if they don't exist
def ensure_directories():
    """Create necessary directories if they don't exist."""
    INPUT_DIR.mkdir(exist_ok=True)
    OUTPUT_DIR.mkdir(exist_ok=True)
    LOGS_DIR.mkdir(exist_ok=True)
    
    # Create subdirectories in output
    (OUTPUT_DIR / "json").mkdir(exist_ok=True)
    (OUTPUT_DIR / "metadata").mkdir(exist_ok=True)
    (OUTPUT_DIR / "errors").mkdir(exist_ok=True)

if __name__ == "__main__":
    ensure_directories()
    print("Directories created successfully!")
