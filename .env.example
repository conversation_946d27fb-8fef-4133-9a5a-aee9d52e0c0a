# Environment Variables Template
# Copy this file to .env and fill in your values

# LlamaParse API Configuration
LLAMA_CLOUD_API_KEY=llx-your-api-key-here

# Server Configuration
SERVER_HOST=127.0.0.1
SERVER_PORT=8000

# Processing Configuration
MAX_FILE_SIZE_MB=50
BATCH_SIZE=5

# Directory Paths (optional - defaults will be used if not set)
# INPUT_DIR=./input_documents
# OUTPUT_DIR=./processed_documents
# LOGS_DIR=./logs

# LlamaParse Settings
LLAMAPARSE_RESULT_TYPE=text
LLAMAPARSE_VERBOSE=true
LLAMAPARSE_NUM_WORKERS=4
LLAMAPARSE_LANGUAGE=en

# Logging Configuration
LOG_LEVEL=INFO
