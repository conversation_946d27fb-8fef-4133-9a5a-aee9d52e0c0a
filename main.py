"""
FastAPI server for on-demand document processing.
"""
import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse
import uvicorn

from document_processor import DocumentProcessor, process_files_in_directory
from config import (
    INPUT_DIR, 
    OUTPUT_DIR, 
    SERVER_HOST, 
    SERVER_PORT, 
    SUPPORTED_EXTENSIONS,
    ensure_directories
)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Document Processing Backend",
    description="LlamaIndex + LlamaParse document processing service",
    version="1.0.0"
)

# Initialize processor
processor = DocumentProcessor()

# Ensure directories exist
ensure_directories()


@app.on_event("startup")
async def startup_event():
    """Initialize the application."""
    logger.info("Document Processing Backend starting up...")
    logger.info(f"Input directory: {INPUT_DIR}")
    logger.info(f"Output directory: {OUTPUT_DIR}")
    logger.info(f"Supported extensions: {SUPPORTED_EXTENSIONS}")


@app.get("/")
async def root():
    """Root endpoint with service information."""
    return {
        "service": "Document Processing Backend",
        "status": "running",
        "version": "1.0.0",
        "supported_formats": list(SUPPORTED_EXTENSIONS),
        "endpoints": {
            "process_all": "/process",
            "process_specific": "/process/{filename}",
            "list_files": "/files",
            "results": "/results",
            "health": "/health"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "directories": {
            "input_exists": INPUT_DIR.exists(),
            "output_exists": OUTPUT_DIR.exists()
        }
    }


@app.get("/files")
async def list_input_files():
    """List all files in the input directory."""
    try:
        files = []
        for file_path in INPUT_DIR.iterdir():
            if file_path.is_file():
                files.append({
                    "filename": file_path.name,
                    "size_bytes": file_path.stat().st_size,
                    "size_mb": round(file_path.stat().st_size / (1024 * 1024), 2),
                    "extension": file_path.suffix.lower(),
                    "supported": file_path.suffix.lower() in SUPPORTED_EXTENSIONS,
                    "modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                })
        
        return {
            "total_files": len(files),
            "supported_files": len([f for f in files if f["supported"]]),
            "files": files
        }
    
    except Exception as e:
        logger.error(f"Error listing files: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error listing files: {str(e)}")


@app.post("/process")
async def process_all_files(background_tasks: BackgroundTasks):
    """Process all supported files in the input directory."""
    try:
        # Find all supported files
        file_paths = []
        for ext in SUPPORTED_EXTENSIONS:
            file_paths.extend(INPUT_DIR.glob(f"*{ext}"))
        
        if not file_paths:
            return {
                "message": "No supported files found in input directory",
                "processed_files": 0,
                "results": []
            }
        
        logger.info(f"Starting processing of {len(file_paths)} files")
        
        # Process files
        results = await processor.process_multiple_files(file_paths)
        
        # Save results
        saved_files = processor.save_results(results)
        
        # Prepare response
        successful_files = [r for r in results if r["processing_info"]["success"]]
        failed_files = [r for r in results if not r["processing_info"]["success"]]
        
        return {
            "message": f"Processing completed for {len(file_paths)} files",
            "processed_files": len(file_paths),
            "successful": len(successful_files),
            "failed": len(failed_files),
            "output_files": [str(f) for f in saved_files],
            "results": results
        }
    
    except Exception as e:
        logger.error(f"Error processing files: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing files: {str(e)}")


@app.post("/process/{filename}")
async def process_specific_file(filename: str):
    """Process a specific file by filename."""
    try:
        file_path = INPUT_DIR / filename
        
        if not file_path.exists():
            raise HTTPException(status_code=404, detail=f"File '{filename}' not found in input directory")
        
        if not file_path.is_file():
            raise HTTPException(status_code=400, detail=f"'{filename}' is not a file")
        
        logger.info(f"Processing specific file: {filename}")
        
        # Process single file
        result = await processor.process_single_file(file_path)
        
        # Save result
        saved_files = processor.save_results([result])
        
        return {
            "message": f"Processing completed for {filename}",
            "success": result["processing_info"]["success"],
            "output_file": str(saved_files[0]) if saved_files else None,
            "result": result
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing file {filename}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")


@app.get("/results")
async def list_results():
    """List all processed result files."""
    try:
        results_dir = OUTPUT_DIR / "json"
        result_files = []
        
        if results_dir.exists():
            for file_path in results_dir.glob("*.json"):
                result_files.append({
                    "filename": file_path.name,
                    "size_bytes": file_path.stat().st_size,
                    "created": datetime.fromtimestamp(file_path.stat().st_ctime).isoformat(),
                    "path": str(file_path)
                })
        
        return {
            "total_results": len(result_files),
            "results": sorted(result_files, key=lambda x: x["created"], reverse=True)
        }
    
    except Exception as e:
        logger.error(f"Error listing results: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error listing results: {str(e)}")


@app.delete("/results")
async def clear_results():
    """Clear all result files."""
    try:
        results_dir = OUTPUT_DIR / "json"
        deleted_count = 0
        
        if results_dir.exists():
            for file_path in results_dir.glob("*.json"):
                file_path.unlink()
                deleted_count += 1
        
        return {
            "message": f"Cleared {deleted_count} result files",
            "deleted_count": deleted_count
        }
    
    except Exception as e:
        logger.error(f"Error clearing results: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error clearing results: {str(e)}")


if __name__ == "__main__":
    print("🚀 Starting Document Processing Backend Server...")
    print(f"📁 Input Directory: {INPUT_DIR}")
    print(f"📁 Output Directory: {OUTPUT_DIR}")
    print(f"🌐 Server will be available at: http://{SERVER_HOST}:{SERVER_PORT}")
    print(f"📚 API Documentation: http://{SERVER_HOST}:{SERVER_PORT}/docs")
    
    uvicorn.run(
        "main:app",
        host=SERVER_HOST,
        port=SERVER_PORT,
        reload=True,
        log_level="info"
    )
