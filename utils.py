"""
Utility functions for the document processing backend.
"""
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

from config import INPUT_DIR, OUTPUT_DIR, SUPPORTED_EXTENSIONS


def get_file_info(file_path: Path) -> Dict[str, Any]:
    """Get detailed information about a file."""
    if not file_path.exists():
        return {"error": "File does not exist"}
    
    stat = file_path.stat()
    return {
        "name": file_path.name,
        "stem": file_path.stem,
        "suffix": file_path.suffix,
        "size_bytes": stat.st_size,
        "size_mb": round(stat.st_size / (1024 * 1024), 2),
        "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
        "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
        "is_supported": file_path.suffix.lower() in SUPPORTED_EXTENSIONS
    }


def find_supported_files(directory: Path) -> List[Path]:
    """Find all supported files in a directory."""
    supported_files = []
    
    if not directory.exists():
        return supported_files
    
    for ext in SUPPORTED_EXTENSIONS:
        supported_files.extend(directory.glob(f"*{ext}"))
        supported_files.extend(directory.glob(f"*{ext.upper()}"))  # Also check uppercase
    
    return sorted(supported_files)


def create_backup(file_path: Path, backup_dir: Optional[Path] = None) -> Path:
    """Create a backup copy of a file."""
    if backup_dir is None:
        backup_dir = OUTPUT_DIR / "backups"
    
    backup_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
    backup_path = backup_dir / backup_name
    
    shutil.copy2(file_path, backup_path)
    return backup_path


def move_processed_file(file_path: Path, archive_dir: Optional[Path] = None) -> Path:
    """Move a processed file to archive directory."""
    if archive_dir is None:
        archive_dir = OUTPUT_DIR / "archive"
    
    archive_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    archive_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
    archive_path = archive_dir / archive_name
    
    shutil.move(str(file_path), str(archive_path))
    return archive_path


def load_json_result(result_file: Path) -> Optional[Dict[str, Any]]:
    """Load a JSON result file."""
    try:
        with open(result_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {result_file}: {e}")
        return None


def get_processing_summary(results_dir: Optional[Path] = None) -> Dict[str, Any]:
    """Get a summary of all processing results."""
    if results_dir is None:
        results_dir = OUTPUT_DIR / "json"
    
    if not results_dir.exists():
        return {"error": "Results directory does not exist"}
    
    result_files = list(results_dir.glob("*.json"))
    
    summary = {
        "total_results": len(result_files),
        "successful": 0,
        "failed": 0,
        "total_text_length": 0,
        "total_word_count": 0,
        "file_types": {},
        "latest_processing": None,
        "oldest_processing": None
    }
    
    processing_times = []
    
    for result_file in result_files:
        result = load_json_result(result_file)
        if not result:
            continue
        
        # Count success/failure
        if result.get("processing_info", {}).get("success", False):
            summary["successful"] += 1
            
            # Add text statistics
            content = result.get("content", {})
            summary["total_text_length"] += content.get("text_length", 0)
            summary["total_word_count"] += content.get("word_count", 0)
        else:
            summary["failed"] += 1
        
        # Count file types
        file_ext = result.get("file_info", {}).get("file_extension", "unknown")
        summary["file_types"][file_ext] = summary["file_types"].get(file_ext, 0) + 1
        
        # Track processing times
        processed_at = result.get("file_info", {}).get("processed_at")
        if processed_at:
            processing_times.append(processed_at)
    
    # Set latest and oldest processing times
    if processing_times:
        processing_times.sort()
        summary["oldest_processing"] = processing_times[0]
        summary["latest_processing"] = processing_times[-1]
    
    return summary


def cleanup_old_results(days_old: int = 30, results_dir: Optional[Path] = None) -> int:
    """Clean up result files older than specified days."""
    if results_dir is None:
        results_dir = OUTPUT_DIR / "json"
    
    if not results_dir.exists():
        return 0
    
    cutoff_time = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
    deleted_count = 0
    
    for result_file in results_dir.glob("*.json"):
        if result_file.stat().st_mtime < cutoff_time:
            result_file.unlink()
            deleted_count += 1
    
    return deleted_count


def validate_input_directory() -> Dict[str, Any]:
    """Validate the input directory and return status."""
    status = {
        "exists": INPUT_DIR.exists(),
        "is_directory": INPUT_DIR.is_dir() if INPUT_DIR.exists() else False,
        "is_readable": False,
        "is_writable": False,
        "file_count": 0,
        "supported_file_count": 0,
        "files": []
    }
    
    if status["exists"] and status["is_directory"]:
        try:
            # Test read access
            list(INPUT_DIR.iterdir())
            status["is_readable"] = True
            
            # Test write access
            test_file = INPUT_DIR / ".test_write"
            test_file.touch()
            test_file.unlink()
            status["is_writable"] = True
            
            # Count files
            all_files = [f for f in INPUT_DIR.iterdir() if f.is_file()]
            supported_files = find_supported_files(INPUT_DIR)
            
            status["file_count"] = len(all_files)
            status["supported_file_count"] = len(supported_files)
            status["files"] = [get_file_info(f) for f in all_files]
            
        except Exception as e:
            status["error"] = str(e)
    
    return status


def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"


def print_processing_report(results: List[Dict[str, Any]]):
    """Print a formatted processing report."""
    print("\n" + "="*60)
    print("📊 DOCUMENT PROCESSING REPORT")
    print("="*60)
    
    successful = [r for r in results if r["processing_info"]["success"]]
    failed = [r for r in results if not r["processing_info"]["success"]]
    
    print(f"📁 Total Files Processed: {len(results)}")
    print(f"✅ Successful: {len(successful)}")
    print(f"❌ Failed: {len(failed)}")
    print(f"📈 Success Rate: {len(successful)/len(results)*100:.1f}%")
    
    if successful:
        total_words = sum(r["content"]["word_count"] for r in successful)
        total_chars = sum(r["content"]["text_length"] for r in successful)
        print(f"📝 Total Words Extracted: {total_words:,}")
        print(f"🔤 Total Characters: {total_chars:,}")
    
    print("\n📋 File Details:")
    for result in results:
        status = "✅" if result["processing_info"]["success"] else "❌"
        filename = result["file_info"]["original_filename"]
        
        if result["processing_info"]["success"]:
            words = result["content"]["word_count"]
            print(f"  {status} {filename} - {words:,} words")
        else:
            error = result["processing_info"].get("error", "Unknown error")
            print(f"  {status} {filename} - Error: {error}")
    
    print("="*60)


if __name__ == "__main__":
    # Test utility functions
    print("Testing utility functions...")
    
    # Validate input directory
    status = validate_input_directory()
    print(f"Input directory status: {status}")
    
    # Get processing summary
    summary = get_processing_summary()
    print(f"Processing summary: {summary}")
